import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      );
    }

    // Check user role for admin privileges (simplified for now)
    // TODO: Implement proper admin role checking based on your user model
    const isAdmin = true; // For now, allow any authenticated user

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    const { id: errorId } = await context.params;

    // Update error as resolved
    const updatedError = await prisma.errorLog.update({
      where: { id: errorId },
      data: {
        resolved: true,
        resolvedAt: new Date(),
        resolvedBy: session.user.id,
      },
    });

    return NextResponse.json({
      success: true,
      error: {
        id: updatedError.id,
        resolved: updatedError.resolved,
        resolvedAt: updatedError.resolvedAt,
        resolvedBy: updatedError.resolvedBy,
      },
    });

  } catch (error) {
    console.error('Error resolving error log:', error);
    return NextResponse.json(
      { error: 'Failed to resolve error' },
      { status: 500 }
    );
  }
}
